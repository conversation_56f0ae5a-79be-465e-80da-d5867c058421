import React from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';

export default function ProfileScreen() {
  const profileOptions = [
    {
      id: 1,
      title: 'اطلاعات شخصی',
      subtitle: 'ویرایش نام، شماره تلفن و آدرس',
      icon: 'person',
      color: '#3498db',
      onPress: () => Alert.alert('اطلاعات شخصی', 'این بخش به زودی راه‌اندازی می‌شود')
    },
    {
      id: 2,
      title: 'تنظیمات امنیتی',
      subtitle: 'تغییر رمز عبور و تنظیمات امنیتی',
      icon: 'lock',
      color: '#e74c3c',
      onPress: () => Alert.alert('تنظیمات امنیتی', 'این بخش به زودی راه‌اندازی می‌شود')
    },
    {
      id: 3,
      title: 'اعلان‌ها',
      subtitle: 'مدیریت اعلان‌ها و پیام‌ها',
      icon: 'bell',
      color: '#f39c12',
      onPress: () => Alert.alert('اعلان‌ها', 'این بخش به زودی راه‌اندازی می‌شود')
    },
    {
      id: 4,
      title: 'پشتیبانی',
      subtitle: 'تماس با پشتیبانی و راهنما',
      icon: 'questionmark.circle',
      color: '#9b59b6',
      onPress: () => Alert.alert('پشتیبانی', 'شماره تماس: ۰۲۱-۱۲۳۴۵۶۷۸')
    },
    {
      id: 5,
      title: 'درباره ما',
      subtitle: 'اطلاعات نرم‌افزار و سازنده',
      icon: 'info.circle',
      color: '#1abc9c',
      onPress: () => Alert.alert('درباره ما', 'نسخه ۱.۰.۰\nساخته شده با React Native')
    },
    {
      id: 6,
      title: 'خروج از حساب',
      subtitle: 'خروج از حساب کاربری',
      icon: 'arrow.right.square',
      color: '#e74c3c',
      onPress: () => Alert.alert(
        'خروج از حساب',
        'آیا می‌خواهید از حساب کاربری خود خارج شوید؟',
        [
          { text: 'انصراف', style: 'cancel' },
          { text: 'خروج', style: 'destructive', onPress: () => Alert.alert('خروج', 'با موفقیت خارج شدید') }
        ]
      )
    }
  ];

  return (
    <ScrollView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedView style={styles.avatarContainer}>
          <IconSymbol name="person.circle.fill" size={80} color="#3498db" />
        </ThemedView>
        <ThemedText type="title" style={styles.userName}>علی موسوی</ThemedText>
        <ThemedText style={styles.userPhone}>۰۹۱۲۳۴۵۶۷۸۹</ThemedText>
      </ThemedView>

      <ThemedView style={styles.optionsContainer}>
        {profileOptions.map((option) => (
          <TouchableOpacity
            key={option.id}
            style={[styles.optionCard, { borderLeftColor: option.color }]}
            onPress={option.onPress}
          >
            <ThemedView style={styles.optionContent}>
              <ThemedView style={[styles.iconContainer, { backgroundColor: option.color + '20' }]}>
                <IconSymbol name={option.icon} size={24} color={option.color} />
              </ThemedView>
              <ThemedView style={styles.optionText}>
                <ThemedText type="subtitle" style={styles.optionTitle}>{option.title}</ThemedText>
                <ThemedText style={styles.optionSubtitle}>{option.subtitle}</ThemedText>
              </ThemedView>
              <IconSymbol name="chevron.left" size={20} color="#999" />
            </ThemedView>
          </TouchableOpacity>
        ))}
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  userName: {
    textAlign: 'center',
    marginBottom: 8,
    color: '#2c3e50',
  },
  userPhone: {
    textAlign: 'center',
    color: '#7f8c8d',
    fontSize: 14,
  },
  optionsContainer: {
    padding: 16,
    gap: 12,
  },
  optionCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  iconContainer: {
    width: 45,
    height: 45,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionText: {
    flex: 1,
    gap: 2,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  optionSubtitle: {
    fontSize: 12,
    color: '#7f8c8d',
  },
});

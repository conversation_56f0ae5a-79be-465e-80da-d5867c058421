import React, { useState } from 'react';
import { Al<PERSON>, Modal, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function HomeScreen() {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState('detailed');

  const handleViolationCheck = () => {
    setModalVisible(true);
  };

  const handleSubmitPlate = () => {
    setModalVisible(false);
    const detailType = selectedMethod === 'detailed' ? 'با جزییات' : 'تجمیعی';
    Alert.alert('در حال بررسی...', `خلافی پلاک ۸۱و۱۹۳-۳۴ ${detailType} در حال بررسی است`);
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#4CAF50" barStyle="light-content" />

      {/* Header with Gradient */}
      <View style={styles.gradientHeader}>
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <Text style={styles.headerIcon}>🔒</Text>
            <Text style={styles.headerIcon}>❓</Text>
            <Text style={styles.headerIcon}>→</Text>
          </View>

          <View style={styles.mainService}>
            <View style={styles.serviceIcon}>
              <Text style={styles.serviceEmoji}>🚗</Text>
            </View>
            <Text style={styles.serviceTitle}>خلافی خودرو</Text>
            <Text style={styles.serviceSubtitle}>برای مشاهده نتیجه، شماره پلاک خودرو یا موتور خود را وارد کنید.</Text>
          </View>

          <View style={styles.plateContainer}>
            <View style={styles.plateRow}>
              <TouchableOpacity style={styles.plateSegment}>
                <Text style={styles.plateText}>📷</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.plateSegment}>
                <Text style={styles.plateText}>🇮🇷</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.plateSegment}>
                <Text style={styles.plateText}>AI</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.plateSegment}>
                <Text style={styles.plateText}>و</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.plateSegment}>
                <Text style={styles.plateText}>۹</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.plateSegment}>
                <Text style={styles.plateText}>۱۹۳</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.plateSegment}>
                <Text style={styles.plateText}>۳۴</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Modal for Plate Input */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setModalVisible(false)}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>

            <Text style={styles.modalTitle}>انتخاب روش استعلام خلافی</Text>
            <Text style={styles.modalSubtitle}>شما در حال استعلام خلافی خودرو با پلاک زیر هستید:</Text>

            {/* Plate Display */}
            <View style={styles.plateDisplay}>
              <View style={styles.plateFlag}>
                <Text style={styles.flagText}>🇮🇷</Text>
              </View>
              <View style={styles.plateNumbers}>
                <Text style={styles.plateNumber}>۸۱</Text>
                <Text style={styles.plateNumber}>و</Text>
                <Text style={styles.plateNumber}>۱۹۳</Text>
                <Text style={styles.plateNumber}>۳۴</Text>
              </View>
              <Text style={styles.iranText}>ایران</Text>
            </View>

            <Text style={styles.methodTitle}>یکی از دو روش زیر را انتخاب کنید:</Text>

            {/* Method Selection */}
            <TouchableOpacity
              style={[styles.methodOption, selectedMethod === 'detailed' && styles.selectedMethod]}
              onPress={() => setSelectedMethod('detailed')}
            >
              <View style={styles.methodIcon}>
                <Text style={styles.methodEmoji}>📋</Text>
              </View>
              <View style={styles.methodContent}>
                <Text style={styles.methodOptionTitle}>خلافی با جزییات</Text>
                <Text style={styles.methodDesc}>نیاز به کد ملی و شماره موبایل مالک</Text>
              </View>
              <View style={[styles.radioButton, selectedMethod === 'detailed' && styles.radioSelected]} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.methodOption, selectedMethod === 'simple' && styles.selectedMethod]}
              onPress={() => setSelectedMethod('simple')}
            >
              <View style={styles.methodIcon}>
                <Text style={styles.methodEmoji}>💳</Text>
              </View>
              <View style={styles.methodContent}>
                <Text style={styles.methodOptionTitle}>خلافی تجمیعی</Text>
                <Text style={styles.methodDesc}>پرداخت مجموع مبلغ خلافی با یک رمز پویا</Text>
              </View>
              <View style={[styles.radioButton, selectedMethod === 'simple' && styles.radioSelected]} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handleSubmitPlate}
            >
              <Text style={styles.confirmButtonText}>تایید</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  gradientHeader: {
    backgroundColor: '#4CAF50',
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 30,
  },
  headerIcon: {
    fontSize: 24,
    color: 'rgba(255,255,255,0.8)',
  },
  mainService: {
    alignItems: 'center',
    marginBottom: 30,
  },
  serviceIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  serviceEmoji: {
    fontSize: 40,
  },
  serviceTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
  },
  serviceSubtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
    lineHeight: 20,
  },
  plateContainer: {
    alignItems: 'center',
  },
  plateRow: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderRadius: 10,
    padding: 5,
    gap: 5,
  },
  plateSegment: {
    backgroundColor: 'white',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 5,
    minWidth: 35,
    alignItems: 'center',
  },
  plateText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    margin: 20,
    width: '90%',
    maxWidth: 400,
  },
  closeButton: {
    position: 'absolute',
    top: 15,
    left: 15,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
    color: '#333',
  },
  modalSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  plateDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  plateFlag: {
    marginRight: 10,
  },
  flagText: {
    fontSize: 20,
  },
  plateNumbers: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 5,
    padding: 8,
    gap: 5,
  },
  plateNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    minWidth: 25,
    textAlign: 'center',
  },
  iranText: {
    marginLeft: 10,
    fontSize: 14,
    color: '#666',
  },
  methodTitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 15,
    color: '#666',
  },
  methodOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedMethod: {
    borderColor: '#4CAF50',
    backgroundColor: '#f0f8f0',
  },
  methodIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  methodEmoji: {
    fontSize: 20,
  },
  methodContent: {
    flex: 1,
  },
  methodOptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  methodDesc: {
    fontSize: 12,
    color: '#666',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#ddd',
    backgroundColor: 'white',
  },
  radioSelected: {
    borderColor: '#4CAF50',
    backgroundColor: '#4CAF50',
  },
  confirmButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

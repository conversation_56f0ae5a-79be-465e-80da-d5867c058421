import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import React, { useState } from 'react';
import { Alert, Modal, ScrollView, StyleSheet, TextInput, TouchableOpacity } from 'react-native';

export default function HomeScreen() {
  const [modalVisible, setModalVisible] = useState(false);
  const [plateNumber, setPlateNumber] = useState('');

  const handleViolationCheck = () => {
    setModalVisible(true);
  };

  const handleSubmitPlate = (withDetails: boolean) => {
    if (!plateNumber.trim()) {
      Alert.alert('خطا', 'لطفاً شماره پلاک را وارد کنید');
      return;
    }
    
    setModalVisible(false);
    const detailType = withDetails ? 'با جزییات' : 'بدون جزییات';
    Alert.alert('در حال بررسی...', `خلافی پلاک ${plateNumber} ${detailType} در حال بررسی است`);
    setPlateNumber('');
  };

  const services = [
    {
      id: 1,
      title: 'خلافی خودرو',
      subtitle: 'استعلام خلافی خودرو و موتور',
      icon: 'car',
      color: '#FF6B6B',
      onPress: handleViolationCheck
    },
    {
      id: 2,
      title: 'خدمات بانکی',
      subtitle: 'انتقال وجه و پرداخت',
      icon: 'creditcard',
      color: '#4ECDC4',
      onPress: () => Alert.alert('خدمات بانکی', 'این بخش به زودی راه‌اندازی می‌شود')
    },
    {
      id: 3,
      title: 'قبض‌ها',
      subtitle: 'پرداخت قبوض مختلف',
      icon: 'doc.text',
      color: '#45B7D1',
      onPress: () => Alert.alert('قبض‌ها', 'این بخش به زودی راه‌اندازی می‌شود')
    },
    {
      id: 4,
      title: 'خدمات دیگر',
      subtitle: 'سایر خدمات مفید',
      icon: 'ellipsis',
      color: '#96CEB4',
      onPress: () => Alert.alert('خدمات دیگر', 'این بخش به زودی راه‌اندازی می‌شود')
    }
  ];

  return (
    <ScrollView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>خوش آمدید</ThemedText>
        <ThemedText style={styles.headerSubtitle}>خدمات خلافی و بانکی</ThemedText>
      </ThemedView>

      <ThemedView style={styles.servicesContainer}>
        {services.map((service) => (
          <TouchableOpacity
            key={service.id}
            style={[styles.serviceCard, { borderLeftColor: service.color }]}
            onPress={service.onPress}
          >
            <ThemedView style={styles.serviceContent}>
              <ThemedView style={[styles.iconContainer, { backgroundColor: service.color + '20' }]}>
                <IconSymbol name={service.icon} size={32} color={service.color} />
              </ThemedView>
              <ThemedView style={styles.serviceText}>
                <ThemedText type="subtitle" style={styles.serviceTitle}>{service.title}</ThemedText>
                <ThemedText style={styles.serviceSubtitle}>{service.subtitle}</ThemedText>
              </ThemedView>
              <IconSymbol name="chevron.left" size={20} color="#999" />
            </ThemedView>
          </TouchableOpacity>
        ))}
      </ThemedView>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <ThemedView style={styles.modalOverlay}>
          <ThemedView style={styles.modalContent}>
            <ThemedText type="subtitle" style={styles.modalTitle}>استعلام خلافی خودرو</ThemedText>
            
            <ThemedText style={styles.inputLabel}>شماره پلاک:</ThemedText>
            <TextInput
              style={styles.plateInput}
              value={plateNumber}
              onChangeText={setPlateNumber}
              placeholder="مثال: 12ج345-67"
              placeholderTextColor="#999"
            />

            <ThemedView style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.detailButton]}
                onPress={() => handleSubmitPlate(true)}
              >
                <ThemedText style={styles.buttonText}>با جزییات</ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.simpleButton]}
                onPress={() => handleSubmitPlate(false)}
              >
                <ThemedText style={styles.buttonText}>بدون جزییات</ThemedText>
              </TouchableOpacity>
            </ThemedView>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setModalVisible(false)}
            >
              <ThemedText style={styles.cancelText}>انصراف</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        </ThemedView>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  headerTitle: {
    textAlign: 'center',
    marginBottom: 8,
    color: '#2c3e50',
  },
  headerSubtitle: {
    textAlign: 'center',
    color: '#7f8c8d',
    fontSize: 14,
  },
  servicesContainer: {
    padding: 16,
    gap: 16,
  },
  serviceCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  serviceContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  serviceText: {
    flex: 1,
    gap: 4,
  },
  serviceTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  serviceSubtitle: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 24,
    minHeight: 300,
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 24,
    color: '#2c3e50',
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 8,
    color: '#2c3e50',
    fontWeight: '600',
  },
  plateInput: {
    borderWidth: 2,
    borderColor: '#e9ecef',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
    fontFamily: 'IRANSans',
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  modalButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  detailButton: {
    backgroundColor: '#3498db',
  },
  simpleButton: {
    backgroundColor: '#2ecc71',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    padding: 16,
    alignItems: 'center',
  },
  cancelText: {
    color: '#e74c3c',
    fontSize: 16,
  },
});

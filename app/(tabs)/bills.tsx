import React from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';

export default function BillsScreen() {
  const bills = [
    {
      id: 1,
      title: 'قبض برق',
      amount: '125,000 تومان',
      dueDate: '۱۵ شهریور ۱۴۰۳',
      status: 'پرداخت نشده',
      color: '#e74c3c',
      icon: 'bolt'
    },
    {
      id: 2,
      title: 'قبض گاز',
      amount: '85,000 تومان',
      dueDate: '۲۰ شهریور ۱۴۰۳',
      status: 'پرداخت شده',
      color: '#27ae60',
      icon: 'flame'
    },
    {
      id: 3,
      title: 'قبض آب',
      amount: '45,000 تومان',
      dueDate: '۲۵ شهریور ۱۴۰۳',
      status: 'پرداخت نشده',
      color: '#e74c3c',
      icon: 'drop'
    },
    {
      id: 4,
      title: 'قبض تلفن',
      amount: '65,000 تومان',
      dueDate: '۳۰ شهریور ۱۴۰۳',
      status: 'پرداخت نشده',
      color: '#e74c3c',
      icon: 'phone'
    }
  ];

  const handlePayBill = (bill: any) => {
    Alert.alert(
      'پرداخت قبض',
      `آیا می‌خواهید قبض ${bill.title} به مبلغ ${bill.amount} را پرداخت کنید؟`,
      [
        { text: 'انصراف', style: 'cancel' },
        { text: 'پرداخت', onPress: () => Alert.alert('موفق', 'قبض با موفقیت پرداخت شد') }
      ]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>قبض‌ها</ThemedText>
        <ThemedText style={styles.headerSubtitle}>مدیریت و پرداخت قبوض</ThemedText>
      </ThemedView>

      <ThemedView style={styles.billsContainer}>
        {bills.map((bill) => (
          <TouchableOpacity
            key={bill.id}
            style={[styles.billCard, { borderLeftColor: bill.color }]}
            onPress={() => handlePayBill(bill)}
          >
            <ThemedView style={styles.billContent}>
              <ThemedView style={[styles.iconContainer, { backgroundColor: bill.color + '20' }]}>
                <IconSymbol name={bill.icon} size={28} color={bill.color} />
              </ThemedView>
              <ThemedView style={styles.billInfo}>
                <ThemedText type="subtitle" style={styles.billTitle}>{bill.title}</ThemedText>
                <ThemedText style={styles.billAmount}>{bill.amount}</ThemedText>
                <ThemedText style={styles.billDueDate}>سررسید: {bill.dueDate}</ThemedText>
                <ThemedText style={[styles.billStatus, { color: bill.color }]}>
                  {bill.status}
                </ThemedText>
              </ThemedView>
              <IconSymbol name="chevron.left" size={20} color="#999" />
            </ThemedView>
          </TouchableOpacity>
        ))}
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  headerTitle: {
    textAlign: 'center',
    marginBottom: 8,
    color: '#2c3e50',
  },
  headerSubtitle: {
    textAlign: 'center',
    color: '#7f8c8d',
    fontSize: 14,
  },
  billsContainer: {
    padding: 16,
    gap: 16,
  },
  billCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  billContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  billInfo: {
    flex: 1,
    gap: 4,
  },
  billTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  billAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#3498db',
  },
  billDueDate: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  billStatus: {
    fontSize: 12,
    fontWeight: '600',
  },
});

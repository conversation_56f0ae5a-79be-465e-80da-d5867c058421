import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import React from 'react';
import { Alert, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

export default function HistoryScreen() {
  const historyItems = [
    {
      id: 1,
      title: 'استعلام خلافی خودرو',
      subtitle: 'پلاک: ۱۲ج۳۴۵-۶۷',
      date: '۱۵ شهریور ۱۴۰۳',
      time: '۱۴:۳۰',
      status: 'موفق',
      icon: 'car',
      color: '#27ae60'
    },
    {
      id: 2,
      title: 'پرداخت قبض برق',
      subtitle: 'مبلغ: ۱۲۵,۰۰۰ تومان',
      date: '۱۴ شهریور ۱۴۰۳',
      time: '۱۰:۱۵',
      status: 'موفق',
      icon: 'bolt',
      color: '#27ae60'
    },
    {
      id: 3,
      title: 'انتقال وجه',
      subtitle: 'مبلغ: ۵۰۰,۰۰۰ تومان',
      date: '۱۳ شهریور ۱۴۰۳',
      time: '۱۶:۴۵',
      status: 'موفق',
      icon: 'creditcard',
      color: '#27ae60'
    },
    {
      id: 4,
      title: 'استعلام خلافی موتور',
      subtitle: 'پلاک: ۱۲۳۴۵-۶۷۸',
      date: '۱۲ شهریور ۱۴۰۳',
      time: '۰۹:۲۰',
      status: 'ناموفق',
      icon: 'car',
      color: '#e74c3c'
    },
    {
      id: 5,
      title: 'پرداخت قبض گاز',
      subtitle: 'مبلغ: ۸۵,۰۰۰ تومان',
      date: '۱۱ شهریور ۱۴۰۳',
      time: '۱۱:۳۰',
      status: 'موفق',
      icon: 'flame',
      color: '#27ae60'
    }
  ];

  const handleItemPress = (item: any) => {
    Alert.alert(
      'جزییات تراکنش',
      `عنوان: ${item.title}\n${item.subtitle}\nتاریخ: ${item.date}\nساعت: ${item.time}\nوضعیت: ${item.status}`
    );
  };

  return (
    <ScrollView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>سوابق</ThemedText>
        <ThemedText style={styles.headerSubtitle}>تاریخچه فعالیت‌ها و تراکنش‌ها</ThemedText>
      </ThemedView>

      <ThemedView style={styles.historyContainer}>
        {historyItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[styles.historyCard, { borderLeftColor: item.color }]}
            onPress={() => handleItemPress(item)}
          >
            <ThemedView style={styles.historyContent}>
              <ThemedView style={[styles.iconContainer, { backgroundColor: item.color + '20' }]}>
                <IconSymbol name={item.icon} size={24} color={item.color} />
              </ThemedView>
              <ThemedView style={styles.historyInfo}>
                <ThemedText type="subtitle" style={styles.historyTitle}>{item.title}</ThemedText>
                <ThemedText style={styles.historySubtitle}>{item.subtitle}</ThemedText>
                <ThemedView style={styles.historyMeta}>
                  <ThemedText style={styles.historyDate}>{item.date} - {item.time}</ThemedText>
                  <ThemedText style={[styles.historyStatus, { color: item.color }]}>
                    {item.status}
                  </ThemedText>
                </ThemedView>
              </ThemedView>
              <IconSymbol name="chevron.left" size={20} color="#999" />
            </ThemedView>
          </TouchableOpacity>
        ))}
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  headerTitle: {
    textAlign: 'center',
    marginBottom: 8,
    color: '#2c3e50',
  },
  headerSubtitle: {
    textAlign: 'center',
    color: '#7f8c8d',
    fontSize: 14,
  },
  historyContainer: {
    padding: 16,
    gap: 12,
  },
  historyCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  historyContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 45,
    height: 45,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  historyInfo: {
    flex: 1,
    gap: 4,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  historySubtitle: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  historyMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  historyDate: {
    fontSize: 12,
    color: '#95a5a6',
  },
  historyStatus: {
    fontSize: 12,
    fontWeight: '600',
  },
});
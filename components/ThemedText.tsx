import { StyleSheet, Text, type TextProps } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link';
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');

  return (
    <Text
      style={[
        { color },
        type === 'default' ? styles.default : undefined,
        type === 'title' ? styles.title : undefined,
        type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
        type === 'subtitle' ? styles.subtitle : undefined,
        type === 'link' ? styles.link : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize: 16,
    lineHeight: 26,
    fontFamily: 'IRANSans',
  },
  defaultSemiBold: {
    fontSize: 16,
    lineHeight: 26,
    fontWeight: '600',
    fontFamily: 'IRANSans',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    lineHeight: 38,
    fontFamily: 'IRANSans',
  },
  subtitle: {
    fontSize: 20,
    fontWeight: 'bold',
    lineHeight: 28,
    fontFamily: 'IRANSans',
  },
  link: {
    lineHeight: 28,
    fontSize: 16,
    color: '#0a7ea4',
    fontFamily: 'IRANSans',
  },
});
